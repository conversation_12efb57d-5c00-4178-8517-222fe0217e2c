// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id           String   @id @default(cuid())
  email        String   @unique
  passwordHash String?  // Optional for OAuth-only users
  name         String?
  avatar       String?
  preferences  Json?    @db.JsonB // User preferences stored as JSONB
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  twitterAccounts TwitterAccount[]
  googleAccounts  GoogleAccount[]
  sessions        Session[]
  scheduledTweets ScheduledTweet[]
  mediaFiles      MediaFile[]
  agents          Agent[]

  @@map("users")
}

model Agent {
  id               String   @id @default(cuid())
  name             String
  description      String
  personaData      Json     @db.JsonB // Complete persona configuration
  personaDefinition Json?    @db.JsonB // Uploaded persona definition JSON
  aiProvider       String   // 'openai' | 'google'
  aiModel          String
  isActive         Boolean  @default(true)
  tweetsGenerated  Int      @default(0)
  engagementRate   Float    @default(0.0)
  maxDailyTweets   Int      @default(10)
  userId           String
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  user            User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  scheduledTweets ScheduledTweet[]
  memories        AgentMemory[]

  @@index([userId])
  @@index([isActive])
  @@map("agents")
}

model TwitterAccount {
  id              String   @id @default(cuid())
  twitterId       String   @unique // Twitter's unique user ID
  username        String   @unique // Twitter handle (e.g., @username)
  displayName     String   // Display name on Twitter
  profileImageUrl String?  // URL to profile image
  accessToken     String   // OAuth access token
  refreshToken    String?  // OAuth refresh token (if available)
  tokenSecret     String?  // OAuth token secret (for OAuth 1.0a)
  isActive        Boolean  @default(true)
  userId          String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user            User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  scheduledTweets ScheduledTweet[]

  @@index([userId])
  @@index([isActive])
  @@index([twitterId])
  @@map("twitter_accounts")
}

model ScheduledTweet {
  id             String    @id @default(cuid())
  content        String
  mediaUrls      String[]  // Array of media URLs
  scheduledFor   DateTime?
  publishedAt    DateTime?
  status         String    @default("draft") // 'draft' | 'scheduled' | 'published' | 'failed'
  twitterTweetId String?   // ID from Twitter API after publishing
  
  // Thread support
  threadId       String?   // Groups tweets into threads
  threadOrder    Int?      // Order within thread (0-based)
  isThreadStart  Boolean   @default(false) // First tweet in thread
  
  // Metrics
  likes          Int       @default(0)
  retweets       Int       @default(0)
  replies        Int       @default(0)
  impressions    Int       @default(0)
  engagementRate Float     @default(0.0)
  
  // Foreign Keys
  agentId          String
  userId           String
  twitterAccountId String?
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  agent          Agent           @relation(fields: [agentId], references: [id], onDelete: Cascade)
  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  twitterAccount TwitterAccount? @relation(fields: [twitterAccountId], references: [id], onDelete: SetNull)

  @@index([agentId])
  @@index([userId])
  @@index([status])
  @@index([scheduledFor])
  @@index([publishedAt])
  @@index([threadId])
  @@index([threadOrder])
  @@map("scheduled_tweets")
}

model AgentMemory {
  id        String   @id @default(cuid())
  content   String
  context   String?  // Additional context about the memory
  embedding Bytes?   @db.ByteA // pgvector embedding stored as bytes
  agentId   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  agent Agent @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@index([agentId])
  // Note: pgvector index will be added via raw SQL in migration
  @@map("agent_memories")
}

model MediaFile {
  id       String   @id @default(cuid())
  filename String
  url      String
  mimeType String
  size     Int
  userId   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([mimeType])
  @@map("media_files")
}

model GoogleAccount {
  id              String   @id @default(cuid())
  googleId        String   @unique // Google's unique user ID
  email           String   @unique // Google email
  name            String   // Display name from Google
  profileImageUrl String?  // URL to profile image
  accessToken     String   // OAuth access token (encrypted)
  refreshToken    String?  // OAuth refresh token (encrypted)
  userId          String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([googleId])
  @@map("google_accounts")
}

model Session {
  id        String   @id @default(cuid())
  jwtId     String   @unique // JWT ID for revocation
  userId    String
  ipAddress String?
  userAgent String?
  expiresAt DateTime
  isRevoked Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([jwtId])
  @@index([expiresAt])
  @@map("sessions")
}