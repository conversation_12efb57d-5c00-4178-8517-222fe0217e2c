# XTask - AI-Powered Social Media Management Platform

A modern monorepo application built with Next.js 15.3.3, React 18, TypeScript 5.6, Express.js, and Prisma ORM with PostgreSQL for AI-powered social media management.

## 🏗️ Project Structure

```
xtask-monorepo/
├── apps/
│   └── web/                 # Next.js 15.3.3 frontend application
│       ├── src/
│       │   ├── app/         # App Router pages
│       │   ├── components/  # Reusable UI components
│       │   ├── lib/         # Utility functions
│       │   └── types/       # TypeScript type definitions
│       └── package.json
├── packages/
│   └── server/              # Express.js 4.21.1 backend server
│       ├── src/
│       │   └── server-simple.ts
│       └── package.json
├── prisma/
│   ├── schema.prisma        # Database schema
│   └── migrations/          # Database migrations
├── lib/
│   └── database.ts          # Database utilities
├── .env.template           # Environment variables template
├── .env.development       # Local development environment
└── package.json           # Root workspace configuration
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm (comes with Node.js)

### Installation

1. **Clone the repository**
   ```bash
git clone <repository-url>
   cd xtask-monorepo
```

2. **Install all dependencies**
   ```bash
npm run install:all
```

3. **Set up environment variables**
   ```bash
cp .env.example .env
   # Edit .env and fill in your actual credentials
   # See Environment Variables section below for detailed setup
```

4. **Set up the database**
   ```bash
npm run db:setup
```

5. **Start development servers**
   ```bash
npm run dev
```

This will start:
- Next.js frontend on `http://localhost:3000`
- Express.js backend on `http://localhost:3030`

## 📦 Available Scripts

### Root Level Commands

- `npm run dev` - Start both frontend and backend in development mode
- `npm run build` - Build the frontend application
- `npm run start` - Start the production server
- `npm run lint` - Run ESLint on the frontend
- `npm run type-check` - Run TypeScript type checking on both apps
- `npm run install:all` - Install dependencies for all workspaces
- `npm run db:setup` - Set up database (generate client + run migrations)
- `npm run db:migrate` - Run database migrations
- `npm run db:generate` - Generate Prisma client
- `npm run db:studio` - Open Prisma Studio (database GUI)
- `npm run db:reset` - Reset database (⚠️ destructive)

### Frontend (apps/web)

- `npm run --workspace=apps/web dev` - Start Next.js development server
- `npm run --workspace=apps/web build` - Build for production
- `npm run --workspace=apps/web start` - Start production server
- `npm run --workspace=apps/web lint` - Run ESLint
- `npm run --workspace=apps/web type-check` - TypeScript type checking

### Backend (packages/server)

- `npm run --workspace=packages/server dev` - Start Express server with hot reload
- `npm run --workspace=packages/server build` - Compile TypeScript to JavaScript
- `npm run --workspace=packages/server start` - Start production server
- `npm run --workspace=packages/server type-check` - TypeScript type checking

## 🛠️ Technology Stack

### Frontend
- **Next.js 15.3.3** - React framework with App Router
- **React 18.3.1** - UI library
- **TypeScript 5.6.2** - Type safety
- **Tailwind CSS 3.4.13** - Utility-first CSS framework
- **Framer Motion 11.11.1** - Animation library
- **React Hook Form 7.53.0** - Form handling
- **Zod 3.23.8** - Schema validation
- **TanStack Query 5.59.0** - Data fetching and caching
- **Zustand 4.5.5** - State management
- **Lucide React 0.460.0** - Icon library

### Backend
- **Express.js 4.21.1** - Web framework
- **Twitter API v2** - Social media integration with OAuth 2.0
- **node-twitter-api-v2 1.17.2** - Twitter API client library
- **TypeScript 5.6.2** - Type safety
- **CORS 2.8.5** - Cross-origin resource sharing
- **Helmet 8.0.0** - Security middleware
- **Compression 1.7.4** - Response compression
- **dotenv 16.4.5** - Environment variable management

### Database
- **PostgreSQL** - Primary database (Neon serverless)
- **Prisma 5.8.0** - ORM and database toolkit
- **pgvector** - Vector similarity search for AI features

### Development Tools
- **tsx 4.19.1** - TypeScript execution for development
- **concurrently 9.0.1** - Run multiple commands concurrently
- **ESLint 8.57.1** - Code linting
- **PostCSS 8.4.47** - CSS processing

## 🔧 Configuration

### TypeScript Configuration

Both frontend and backend have their own `tsconfig.json` files with:
- Strict type checking enabled
- Path aliases configured (`@/*` maps to `./src/*`)
- Modern ES2022 target for server, ES2017 for client
- Proper module resolution

### Environment Variables

Copy `.env.example` to `.env` and configure all required variables:

```bash
cp .env.example .env
```

**Required Environment Variables:**

1. **Database Configuration**
   ```env
DATABASE_URL="your-neon-postgresql-url"
   DIRECT_URL="your-neon-direct-url"
```

2. **Authentication & Session Management**
   ```env
NEXTAUTH_SECRET="your-32-char-secret"  # Generate with: openssl rand -base64 32
   NEXTAUTH_URL="http://localhost:3000"
```

3. **OAuth Providers**
   ```env
# Google OAuth
   GOOGLE_CLIENT_ID="your-google-client-id"
   GOOGLE_CLIENT_SECRET="your-google-client-secret"

   # Twitter/X OAuth
   TWITTER_CLIENT_ID="your-twitter-client-id"
   TWITTER_CLIENT_SECRET="your-twitter-client-secret"
```

4. **Media Upload Service**
   ```env
UPLOADTHING_SECRET="your-uploadthing-secret"
   UPLOADTHING_APP_ID="your-uploadthing-app-id"
   UPLOADTHING_TOKEN="your-uploadthing-token"
```

5. **AI Providers (at least one required)**
   ```env
OPENAI_API_KEY="your-openai-api-key"
   GEMINI_API_KEY="your-gemini-api-key"
   # Optional: MISTRAL_API_KEY, HUGGINGFACE_API_KEY, GROQ_API_KEY, OPENROUTER_API_KEY
```

**Environment Validation:**
Run the validation script to check your configuration:
```bash
npm run validate-env
```

### Twitter Developer Portal Setup

1. **Create Twitter Developer Account**
   - Visit [developer.twitter.com](https://developer.twitter.com)
   - Apply for a developer account
   - Create a new project/app

2. **Configure OAuth 2.0**
   - Set app type to "Web App"
   - Add callback URL: `http://localhost:3000/api/auth/twitter/callback`
   - Enable OAuth 2.0 with PKCE
   - Request scopes: `tweet.read`, `tweet.write`, `users.read`, `offline.access`

3. **Get API Credentials**
   - Copy Client ID and Client Secret
   - Generate Bearer Token
   - Add credentials to `.env.development`

## 🗄️ Database Schema

The application uses PostgreSQL with the following main entities:

- **Users** - User accounts and profiles
- **Agents** - AI agents with personalities and configurations
- **TwitterAccounts** - Connected Twitter accounts
- **ScheduledTweets** - Tweets with scheduling and metrics
- **AgentMemory** - AI agent memories with vector embeddings
- **MediaFiles** - Uploaded media files

### Database Commands

```bash
# Set up database (first time)
npm run db:setup

# Generate Prisma client after schema changes
npm run db:generate

# Create and run new migration
npm run db:migrate

# Open database GUI
npm run db:studio

# Reset database (⚠️ destructive)
npm run db:reset
```

## 🏃‍♂️ Development Workflow

1. **Set up the project**
   ```bash
npm run install:all
   npm run db:setup
```

2. **Start development environment**
   ```bash
npm run dev
```

3. **Make changes to code**
   - Frontend changes will hot-reload automatically
   - Backend changes will restart the server automatically
   - Database schema changes require running `npm run db:migrate`

4. **Type checking**
   ```bash
npm run type-check
```

5. **Linting**
   ```bash
npm run lint
```

## 🚀 Production Deployment

1. **Build the application**
   ```bash
npm run build
```

2. **Start production server**
   ```bash
npm run start
```

## 📝 API Endpoints

The Express.js server provides the following endpoints:

- `GET /api/health` - Health check with database status
- `GET /api/stats` - Database statistics
- `GET /api/hello` - Test endpoint

### Twitter API v2 Endpoints

- `GET /api/twitter/test` - Check Twitter connection status
- `POST /api/twitter/test` - Post test tweet
- `POST /api/twitter/thread` - Post Twitter thread
- `GET /api/twitter/media` - Get media upload limits
- `POST /api/twitter/media` - Upload media to Twitter

## 🔒 Security Features

- **Secure database connections** with SSL/TLS
- **Environment variable management** for sensitive data
- **Prepared statements** via Prisma ORM
- **Connection pooling** for optimal performance
- **Input validation** and sanitization
- **OAuth 2.0 with PKCE** for Twitter authentication
- **Encrypted token storage** for social media credentials
- **Rate limiting** for API endpoints
- **CSRF protection** for forms

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and type checking
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.