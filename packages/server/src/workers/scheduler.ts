import cron from 'node-cron';
import { PrismaClient } from '@prisma/client';
import { createTwitterServiceForAccount } from '../services/twitter-client';

const prisma = new PrismaClient();

const processScheduledTweets = async () => {
  console.log('Scheduler worker checking for due tweets...');

  const dueTweets = await prisma.scheduledTweet.findMany({
    where: {
      status: 'scheduled',
      scheduledFor: {
        lte: new Date(),
      },
    },
    include: {
      twitterAccount: true,
      user: true,
    },
  });

  if (dueTweets.length === 0) {
    console.log('No due tweets found.');
    return;
  }

  console.log(`Found ${dueTweets.length} due tweets to process.`);

  for (const tweet of dueTweets) {
    if (!tweet.twitterAccount) {
      console.error(`Tweet ${tweet.id} is missing a Twitter account. Marking as failed.`);
      await prisma.scheduledTweet.update({
        where: { id: tweet.id },
        data: { status: 'failed' },
      });
      continue;
    }

    try {
      const { accessToken, refreshToken } = tweet.twitterAccount;
      if (!accessToken) {
        throw new Error('Twitter account is missing access token.');
      }

      // Create Twitter service for this user's account using OAuth 2.0
      const twitterService = createTwitterServiceForAccount(
        accessToken,
        refreshToken || undefined
      );

      // Post the tweet using the service
      const twitterTweetId = await twitterService.postTweet(tweet.content, tweet.mediaUrls);

      await prisma.scheduledTweet.update({
        where: { id: tweet.id },
        data: {
          status: 'published',
          twitterTweetId,
          publishedAt: new Date(),
        },
      });

      console.log(`Successfully published tweet ${tweet.id} for user ${tweet.userId}. Twitter ID: ${twitterTweetId}`);

    } catch (error: any) {
      const errorMessage = error.message || 'An unknown error occurred.';
      console.error(`Failed to publish tweet ${tweet.id}: ${errorMessage}`);

      await prisma.scheduledTweet.update({
        where: { id: tweet.id },
        data: { status: 'failed' },
      });
    }
  }
};

export const startScheduler = () => {
  const cronInterval = process.env.SCHEDULER_INTERVAL || '*/1 * * * *';

  if (process.env.SCHEDULER_ENABLED !== 'true') {
    console.warn('Scheduler is disabled. Set SCHEDULER_ENABLED=true in .env to enable it.');
    return;
  }

  cron.schedule(cronInterval, processScheduledTweets, {
    scheduled: true,
    timezone: 'UTC',
  });

  console.log(`Tweet scheduler started with interval: ${cronInterval}`);
};
