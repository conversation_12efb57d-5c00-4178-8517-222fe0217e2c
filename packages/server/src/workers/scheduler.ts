import cron from 'node-cron';
import { prisma } from '@/lib/database/prisma';
import { TwitterApi } from 'twitter-api-v2';
import { logger } from '@/lib/logger';

const processScheduledTweets = async () => {
  logger.info('Scheduler worker checking for due tweets...');

  const dueTweets = await prisma.scheduledTweet.findMany({
    where: {
      status: 'scheduled',
      scheduledFor: {
        lte: new Date(),
      },
    },
    include: {
      twitterAccount: true,
      user: true,
    },
  });

  if (dueTweets.length === 0) {
    logger.info('No due tweets found.');
    return;
  }

  logger.info(`Found ${dueTweets.length} due tweets to process.`);

  for (const tweet of dueTweets) {
    if (!tweet.twitterAccount) {
      logger.error(`Tweet ${tweet.id} is missing a Twitter account. Marking as failed.`);
      await prisma.scheduledTweet.update({
        where: { id: tweet.id },
        data: { status: 'failed', errorMessage: 'Twitter account not found.' },
      });
      continue;
    }

    try {
      const { accessToken, accessSecret } = tweet.twitterAccount;
      if (!accessToken || !accessSecret) {
        throw new Error('Twitter account is missing access tokens.');
      }

      const client = new TwitterApi({
        appKey: process.env.TWITTER_CLIENT_ID!,
        appSecret: process.env.TWITTER_CLIENT_SECRET!,
        accessToken,
        accessSecret,
      });

      const { data: createdTweet } = await client.v2.tweet(tweet.content, {
        media: {
          media_ids: tweet.mediaUrls || [],
        },
      });

      await prisma.scheduledTweet.update({
        where: { id: tweet.id },
        data: {
          status: 'published',
          twitterTweetId: createdTweet.id,
          publishedAt: new Date(),
        },
      });

      logger.info(`Successfully published tweet ${tweet.id} for user ${tweet.userId}. Twitter ID: ${createdTweet.id}`);

    } catch (error: any) {
      const errorMessage = error.message || 'An unknown error occurred.';
      logger.error(`Failed to publish tweet ${tweet.id}: ${errorMessage}`);

      await prisma.scheduledTweet.update({
        where: { id: tweet.id },
        data: { status: 'failed', errorMessage },
      });
    }
  }
};

export const startScheduler = () => {
  const cronInterval = process.env.SCHEDULER_INTERVAL || '*/1 * * * *';

  if (process.env.SCHEDULER_ENABLED !== 'true') {
    logger.warn('Scheduler is disabled. Set SCHEDULER_ENABLED=true in .env to enable it.');
    return;
  }

  cron.schedule(cronInterval, processScheduledTweets, {
    scheduled: true,
    timezone: 'UTC',
  });

  logger.info(`Tweet scheduler started with interval: ${cronInterval}`);
};
