import * as React from 'react';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { MoreHorizontal, Edit, Trash2, MessageSquare, Link2 } from 'lucide-react';
import { useDeleteDraft } from '@/hooks/use-drafts';
import { formatDate } from '@/lib/utils';
import type { Draft } from '@/types/draft';

interface DraftCardProps {
  draft: Draft;
  onEdit: (draft: Draft) => void;
  onViewThread?: (threadId: string) => void;
}

export function DraftCard({ draft, onEdit, onViewThread }: DraftCardProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const deleteDraft = useDeleteDraft();

  const handleDelete = () => {
    deleteDraft.mutate(draft.id, {
      onSuccess: () => setShowDeleteDialog(false),
    });
  };

  const truncatedContent = draft.content.length > 100 
    ? `${draft.content.slice(0, 100)}...` 
    : draft.content;

  return (
    <>
      <Card className="group hover:shadow-lg transition-all duration-200 hover:border-primary-500/20">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-gradient-primary text-white text-xs">
                  {draft.agent?.name?.slice(0, 2).toUpperCase() || 'DR'}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <CardTitle className="text-sm font-medium truncate">
                  {draft.agent?.name || 'Unknown Agent'}
                </CardTitle>
                <CardDescription className="text-xs">
                  {formatDate(new Date(draft.createdAt))}
                </CardDescription>
              </div>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onEdit(draft)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Draft
                </DropdownMenuItem>
                {draft.threadId && onViewThread && (
                  <DropdownMenuItem onClick={() => onViewThread(draft.threadId!)}>
                    <Link2 className="mr-2 h-4 w-4" />
                    View Thread
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setShowDeleteDialog(true)}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Draft
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>

        <CardContent className="space-y-3">
          {/* Content Preview */}
          <div className="text-sm text-foreground">
            {truncatedContent}
          </div>

          {/* Media Preview */}
          {draft.mediaUrls && draft.mediaUrls.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {draft.mediaUrls.slice(0, 2).map((url, index) => (
                <div key={index} className="w-12 h-12 rounded overflow-hidden bg-muted">
                  <img
                    src={url}
                    alt={`Media ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
              {draft.mediaUrls.length > 2 && (
                <div className="w-12 h-12 rounded bg-muted flex items-center justify-center text-xs text-muted-foreground">
                  +{draft.mediaUrls.length - 2}
                </div>
              )}
            </div>
          )}

          {/* Thread Info */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {draft.threadId && (
                <Badge variant="outline" className="text-xs">
                  <MessageSquare className="mr-1 h-3 w-3" />
                  Thread {draft.threadOrder !== undefined ? `${draft.threadOrder + 1}` : ''}
                  {draft.isThreadStart && ' (Start)'}
                </Badge>
              )}
              <Badge variant="secondary" className="text-xs">
                Draft
              </Badge>
            </div>
            
            <div className="text-xs text-muted-foreground">
              {draft.content.length}/280
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Draft</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this draft? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700"
              disabled={deleteDraft.isPending}
            >
              {deleteDraft.isPending ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}