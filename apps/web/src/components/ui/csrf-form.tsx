import * as React from 'react'
import { useCSRF } from '@/hooks/use-csrf'

interface CSRFFormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  children: React.ReactNode
}

/**
 * Form component that automatically includes CSRF protection
 */
export function CSRFForm({ children, ...props }: CSRFFormProps) {
  const { getCSRFField, isLoading } = useCSRF()
  const csrfField = getCSRFField()

  if (isLoading) {
    return (
      <form {...props}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
        </div>
        {children}
      </form>
    )
  }

  return (
    <form {...props}>
      {csrfField && (
        <input
          type="hidden"
          name={csrfField.name}
          value={csrfField.value}
        />
      )}
      {children}
    </form>
  )
}

/**
 * Hook to get CSRF-protected fetch function
 */
export function useCSRFProtectedFetch() {
  const { createFetchOptions } = useCSRF()

  const csrfFetch = React.useCallback(
    async (url: string, options: RequestInit = {}) => {
      const fetchOptions = createFetchOptions(options)
      return fetch(url, fetchOptions)
    },
    [createFetchOptions]
  )

  return csrfFetch
}