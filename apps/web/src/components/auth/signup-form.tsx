'use client'

import * as React from 'react'
import { useState } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { useAuth } from '@/hooks/use-auth'
import { useToast } from '@/hooks/use-toast'
import { Loader2, Bot, Mail, Lock, User } from 'lucide-react'

// Google and Twitter SVG icons for proper branding
const GoogleIcon = () => (
  <svg className="h-4 w-4" viewBox="0 0 24 24">
    <path
      fill="currentColor"
      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
    />
    <path
      fill="currentColor"
      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
    />
    <path
      fill="currentColor"
      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
    />
    <path
      fill="currentColor"
      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
    />
  </svg>
)

const TwitterIcon = () => (
  <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
  </svg>
)

export function SignupForm() {
  const [isLoading, setIsLoading] = useState<string | null>(null)
  const [name, setName] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const { login } = useAuth()
  const { toast } = useToast()

  const handleOAuthSignup = async (provider: 'google' | 'twitter') => {
    try {
      setIsLoading(provider)
      // Use the same OAuth flow as login - the backend will handle new user creation
      login(provider)
    } catch (error) {
      console.error('OAuth signup error:', error)
      toast({
        variant: 'destructive',
        title: 'Signup Failed',
        description: 'An error occurred while trying to sign up. Please try again.',
      })
      setIsLoading(null)
    }
  }

  const handleEmailSignup = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!name || !email || !password) {
      toast({
        variant: 'destructive',
        title: 'Missing Information',
        description: 'Please fill in all fields.',
      })
      return
    }

    try {
      setIsLoading('email')

      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, email, password }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Registration failed')
      }

      // Redirect to dashboard on success
      window.location.href = '/dashboard'
    } catch (error) {
      console.error('Email signup error:', error)
      toast({
        variant: 'destructive',
        title: 'Signup Failed',
        description: error instanceof Error ? error.message : 'An error occurred during registration.',
      })
    } finally {
      setIsLoading(null)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-bg p-4">
      <Card className="w-full max-w-md bg-dark-surface border-dark-border">
        <CardHeader className="text-center space-y-4">
          <div className="flex justify-center">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-primary">
              <Bot className="h-6 w-6 text-white" />
            </div>
          </div>
          <div>
            <CardTitle className="text-2xl font-bold text-dark-text">Create Your Account</CardTitle>
            <CardDescription className="mt-2 text-dark-text-muted">
              Get started with XTask and create your first AI-powered social media agent
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Email/Password Form */}
          <form onSubmit={handleEmailSignup} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-dark-text">Full Name</Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-dark-text-muted" />
                <Input
                  id="name"
                  type="text"
                  placeholder="Enter your full name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="pl-10 bg-dark-surface border-dark-border text-dark-text placeholder:text-dark-text-muted focus:border-primary-500"
                  disabled={!!isLoading}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="text-dark-text">Email</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-dark-text-muted" />
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="pl-10 bg-dark-surface border-dark-border text-dark-text placeholder:text-dark-text-muted focus:border-primary-500"
                  disabled={!!isLoading}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="password" className="text-dark-text">Password</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-dark-text-muted" />
                <Input
                  id="password"
                  type="password"
                  placeholder="Create a secure password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pl-10 bg-dark-surface border-dark-border text-dark-text placeholder:text-dark-text-muted focus:border-primary-500"
                  disabled={!!isLoading}
                  required
                />
              </div>
              <p className="text-xs text-dark-text-muted">
                Password must be at least 8 characters with uppercase, lowercase, number, and special character.
              </p>
            </div>

            <Button
              type="submit"
              disabled={!!isLoading}
              className="w-full h-11 bg-primary-500 hover:bg-primary-600 text-white"
            >
              {isLoading === 'email' ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              Create Account
            </Button>
          </form>

          <Separator className="bg-dark-border" />

          {/* OAuth Buttons */}
          <div className="space-y-3">
            <Button
              onClick={() => handleOAuthSignup('google')}
              disabled={!!isLoading}
              variant="outline"
              className="w-full h-11 border-dark-border bg-dark-surface hover:bg-dark-border text-dark-text"
            >
              {isLoading === 'google' ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <GoogleIcon />
              )}
              <span className="ml-2">Sign up with Google</span>
            </Button>

            <Button
              onClick={() => handleOAuthSignup('twitter')}
              disabled={!!isLoading}
              variant="outline"
              className="w-full h-11 border-dark-border bg-dark-surface hover:bg-dark-border text-dark-text"
            >
              {isLoading === 'twitter' ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <TwitterIcon />
              )}
              <span className="ml-2">Sign up with Twitter</span>
            </Button>
          </div>

          <div className="text-center space-y-4">
            <p className="text-sm text-dark-text-muted">
              Already have an account?{' '}
              <Link href="/login" className="text-primary-500 hover:text-primary-400 hover:underline font-medium">
                Sign in here
              </Link>
            </p>

            <p className="text-xs text-dark-text-muted">
              By signing up, you agree to our{' '}
              <a href="/terms" className="text-primary-500 hover:text-primary-400 hover:underline">
                Terms of Service
              </a>{' '}
              and{' '}
              <a href="/privacy" className="text-primary-500 hover:text-primary-400 hover:underline">
                Privacy Policy
              </a>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
