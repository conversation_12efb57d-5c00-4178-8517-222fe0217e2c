import * as React from 'react'
import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { useAuth } from '@/hooks/use-auth'
import { useToast } from '@/hooks/use-toast'
import { Chrome, Twitter, Zap } from 'lucide-react'

export function LoginForm() {
  const [isLoading, setIsLoading] = useState<string | null>(null)
  const { login } = useAuth()
  const { toast } = useToast()

  const handleLogin = async (provider: 'google' | 'twitter') => {
    try {
      setIsLoading(provider)
      login(provider)
    } catch (error) {
      console.error('Login error:', error)
      toast({
        variant: 'error',
        title: 'Login Failed',
        description: 'An error occurred while trying to log in. Please try again.',
      })
      setIsLoading(null)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-bg p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center space-y-4">
          <div className="flex justify-center">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-primary">
              <Zap className="h-6 w-6 text-white" />
            </div>
          </div>
          <div>
            <CardTitle className="text-2xl font-bold">Welcome to XTask</CardTitle>
            <CardDescription className="mt-2">
              Sign in to your account to manage your AI-powered social media agents
            </CardDescription>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <Button
            onClick={() => handleLogin('google')}
            disabled={!!isLoading}
            loading={isLoading === 'google'}
            variant="outline"
            className="w-full"
          >
            <Chrome className="mr-2 h-4 w-4" />
            Continue with Google
          </Button>

          <Button
            onClick={() => handleLogin('twitter')}
            disabled={!!isLoading}
            loading={isLoading === 'twitter'}
            variant="outline"
            className="w-full"
          >
            <Twitter className="mr-2 h-4 w-4" />
            Continue with Twitter
          </Button>

          <Separator className="my-6" />

          <div className="text-center text-sm text-muted-foreground">
            <p>
              By signing in, you agree to our{' '}
              <a href="/terms" className="text-primary hover:underline">
                Terms of Service
              </a>{' '}
              and{' '}
              <a href="/privacy" className="text-primary hover:underline">
                Privacy Policy
              </a>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}