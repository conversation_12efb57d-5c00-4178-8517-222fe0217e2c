import * as React from 'react'
import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Spinner } from '@/components/ui/spinner'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import {
  useAgentMemories,
  useCreateMemory,
  useUpdateMemory,
  useDeleteMemory,
  useSearchMemories,
  type AgentMemory,
} from '@/hooks/use-agent-memory'
import { formatDate } from '@/lib/utils'
import { Brain, Search, Plus, Edit, Trash2, Save, X, <PERSON>rk<PERSON> } from 'lucide-react'

interface AgentMemoryPanelProps {
  agentId: string
  className?: string
}

export function AgentMemoryPanel({ agentId, className }: AgentMemoryPanelProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingMemory, setEditingMemory] = useState<AgentMemory | null>(null)
  const [memoryToDelete, setMemoryToDelete] = useState<string | null>(null)
  const [memoryContent, setMemoryContent] = useState('')
  const [memoryContext, setMemoryContext] = useState('')
  const [activeTab, setActiveTab] = useState('all')

  const { data, isLoading, error } = useAgentMemories(agentId)
  const searchMemories = useSearchMemories(agentId)
  const createMemory = useCreateMemory(agentId)
  const updateMemory = useUpdateMemory(agentId)
  const deleteMemory = useDeleteMemory(agentId)

  const handleSearch = () => {
    if (searchQuery.trim()) {
      searchMemories.mutate({ query: searchQuery })
    }
  }

  const handleCreateMemory = () => {
    if (memoryContent.trim()) {
      createMemory.mutate(
        {
          content: memoryContent,
          context: memoryContext || undefined,
        },
        {
          onSuccess: () => {
            setMemoryContent('')
            setMemoryContext('')
            setShowCreateForm(false)
          },
        }
      )
    }
  }

  const handleUpdateMemory = () => {
    if (editingMemory && memoryContent.trim()) {
      updateMemory.mutate(
        {
          memoryId: editingMemory.id,
          data: {
            content: memoryContent,
            context: memoryContext || undefined,
          },
        },
        {
          onSuccess: () => {
            setMemoryContent('')
            setMemoryContext('')
            setEditingMemory(null)
          },
        }
      )
    }
  }

  const handleDeleteMemory = () => {
    if (memoryToDelete) {
      deleteMemory.mutate(memoryToDelete, {
        onSuccess: () => {
          setMemoryToDelete(null)
        },
      })
    }
  }

  const startEdit = (memory: AgentMemory) => {
    setEditingMemory(memory)
    setMemoryContent(memory.content)
    setMemoryContext(memory.context || '')
  }

  const cancelEdit = () => {
    setEditingMemory(null)
    setMemoryContent('')
    setMemoryContext('')
  }

  const memories = React.useMemo(() => {
    if (activeTab === 'search' && searchMemories.data) {
      return searchMemories.data.memories
    }
    return data?.memories || []
  }, [activeTab, data?.memories, searchMemories.data])

  const isSearching = searchMemories.isPending
  const isCreating = createMemory.isPending
  const isUpdating = updateMemory.isPending
  const isDeleting = deleteMemory.isPending

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Brain className="h-5 w-5 text-primary" />
          <span>Agent Memory</span>
        </CardTitle>
        <CardDescription>
          Vector-based memory storage for agent context and learning
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <div className="flex items-center justify-between mb-4">
            <TabsList>
              <TabsTrigger value="all">All Memories</TabsTrigger>
              <TabsTrigger value="search">Search</TabsTrigger>
            </TabsList>
            <Button
              size="sm"
              onClick={() => setShowCreateForm(true)}
              disabled={showCreateForm}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Memory
            </Button>
          </div>

          <TabsContent value="all" className="space-y-4">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Spinner size="lg" />
              </div>
            ) : error ? (
              <div className="text-center py-8 text-red-500">
                Failed to load memories
              </div>
            ) : memories.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Brain className="h-12 w-12 mx-auto mb-4 opacity-20" />
                <p>No memories found for this agent.</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={() => setShowCreateForm(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Memory
                </Button>
              </div>
            ) : (
              <div className="space-y-3">
                {memories.map((memory) => (
                  <div
                    key={memory.id}
                    className="p-3 rounded-lg bg-dark-surface/50 border border-dark-border"
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex items-center space-x-2">
                        {memory.context && (
                          <Badge variant="outline" className="text-xs">
                            {memory.context}
                          </Badge>
                        )}
                        <span className="text-xs text-muted-foreground">
                          {formatDate(new Date(memory.createdAt))}
                        </span>
                      </div>
                      <div className="flex space-x-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7"
                          onClick={() => startEdit(memory)}
                        >
                          <Edit className="h-3.5 w-3.5" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 text-red-500 hover:text-red-600"
                          onClick={() => setMemoryToDelete(memory.id)}
                        >
                          <Trash2 className="h-3.5 w-3.5" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-sm whitespace-pre-wrap">{memory.content}</p>
                    {memory.similarity !== undefined && (
                      <div className="mt-2 text-xs text-muted-foreground">
                        Similarity: {(memory.similarity * 100).toFixed(1)}%
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {data?.stats && (
              <div className="mt-4 pt-4 border-t border-dark-border">
                <h4 className="text-sm font-medium mb-2">Memory Statistics</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Total Memories</p>
                    <p className="font-medium">{data.stats.totalMemories}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Avg. Length</p>
                    <p className="font-medium">{data.stats.averageContentLength} chars</p>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="search" className="space-y-4">
            <div className="flex space-x-2">
              <Input
                placeholder="Search memories..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1"
              />
              <Button
                onClick={handleSearch}
                disabled={!searchQuery.trim() || isSearching}
              >
                {isSearching ? (
                  <Spinner size="sm" className="mr-2" />
                ) : (
                  <Search className="h-4 w-4 mr-2" />
                )}
                Search
              </Button>
            </div>

            {isSearching ? (
              <div className="flex items-center justify-center py-8">
                <Spinner size="lg" />
              </div>
            ) : searchMemories.data ? (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    Found {searchMemories.data.memories.length} results for "{searchMemories.data.query}"
                  </p>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => searchMemories.reset()}
                  >
                    Clear Results
                  </Button>
                </div>

                {searchMemories.data.memories.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Search className="h-12 w-12 mx-auto mb-4 opacity-20" />
                    <p>No matching memories found.</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {searchMemories.data.memories.map((memory) => (
                      <div
                        key={memory.id}
                        className="p-3 rounded-lg bg-dark-surface/50 border border-dark-border"
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div className="flex items-center space-x-2">
                            {memory.context && (
                              <Badge variant="outline" className="text-xs">
                                {memory.context}
                              </Badge>
                            )}
                            <span className="text-xs text-muted-foreground">
                              {formatDate(new Date(memory.createdAt))}
                            </span>
                          </div>
                          <div className="flex space-x-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-7 w-7"
                              onClick={() => startEdit(memory)}
                            >
                              <Edit className="h-3.5 w-3.5" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-7 w-7 text-red-500 hover:text-red-600"
                              onClick={() => setMemoryToDelete(memory.id)}
                            >
                              <Trash2 className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                        </div>
                        <p className="text-sm whitespace-pre-wrap">{memory.content}</p>
                        <div className="mt-2 text-xs text-muted-foreground">
                          Similarity: {(memory.similarity! * 100).toFixed(1)}%
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ) : searchMemories.error ? (
              <div className="text-center py-8 text-red-500">
                Search failed: {searchMemories.error.message}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Search className="h-12 w-12 mx-auto mb-4 opacity-20" />
                <p>Enter a search query to find relevant memories.</p>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Create/Edit Memory Form */}
        {(showCreateForm || editingMemory) && (
          <div className="mt-4 p-4 border border-dark-border rounded-lg bg-dark-surface/50">
            <h3 className="text-sm font-medium mb-3">
              {editingMemory ? 'Edit Memory' : 'Create New Memory'}
            </h3>
            <div className="space-y-3">
              <div>
                <label className="text-xs text-muted-foreground mb-1 block">
                  Memory Content
                </label>
                <Textarea
                  value={memoryContent}
                  onChange={(e) => setMemoryContent(e.target.value)}
                  placeholder="Enter memory content..."
                  rows={4}
                />
              </div>
              <div>
                <label className="text-xs text-muted-foreground mb-1 block">
                  Context (Optional)
                </label>
                <Input
                  value={memoryContext}
                  onChange={(e) => setMemoryContext(e.target.value)}
                  placeholder="e.g., conversation, preference, fact"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setShowCreateForm(false)
                    setEditingMemory(null)
                    setMemoryContent('')
                    setMemoryContext('')
                  }}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={editingMemory ? handleUpdateMemory : handleCreateMemory}
                  disabled={!memoryContent.trim() || isCreating || isUpdating}
                >
                  {isCreating || isUpdating ? (
                    <Spinner size="sm" className="mr-2" />
                  ) : editingMemory ? (
                    <Save className="h-4 w-4 mr-2" />
                  ) : (
                    <Plus className="h-4 w-4 mr-2" />
                  )}
                  {editingMemory ? 'Update Memory' : 'Create Memory'}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Memory Features */}
        <div className="mt-4 pt-4 border-t border-dark-border">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium">Memory Features</h4>
            <Badge variant="outline" className="text-xs">
              <Sparkles className="h-3 w-3 mr-1 text-yellow-500" />
              Vector Embeddings
            </Badge>
          </div>
          <p className="text-xs text-muted-foreground mb-2">
            Agent memories are stored as vector embeddings for semantic search and retrieval.
            This enables the agent to recall relevant information based on context.
          </p>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="p-2 rounded bg-dark-surface">
              <p className="font-medium mb-1">Semantic Search</p>
              <p className="text-muted-foreground">Find memories by meaning, not just keywords</p>
            </div>
            <div className="p-2 rounded bg-dark-surface">
              <p className="font-medium mb-1">Contextual Recall</p>
              <p className="text-muted-foreground">Memories influence agent responses</p>
            </div>
          </div>
        </div>
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!memoryToDelete} onOpenChange={(open) => !open && setMemoryToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Memory</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this memory? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteMemory}
              className="bg-red-600 hover:bg-red-700"
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  )
}