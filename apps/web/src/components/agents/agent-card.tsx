import * as React from 'react'
import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Bot, MoreHorizontal, Edit, Trash2, Zap, TrendingUp } from 'lucide-react'
import { useToggleAgentStatus, useDeleteAgent } from '@/hooks/use-agents'
import { formatNumber } from '@/lib/utils'
import type { Agent } from '@/types/agent'
import { AI_PROVIDERS } from '@/types/agent'

interface AgentCardProps {
  agent: Agent
  onEdit: (agent: Agent) => void
}

export function AgentCard({ agent, onEdit }: AgentCardProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const toggleStatus = useToggleAgentStatus()
  const deleteAgent = useDeleteAgent()

  const handleStatusToggle = (checked: boolean) => {
    toggleStatus.mutate({ id: agent.id, isActive: checked })
  }

  const handleDelete = () => {
    deleteAgent.mutate(agent.id, {
      onSuccess: () => setShowDeleteDialog(false),
    })
  }

  const providerInfo = AI_PROVIDERS[agent.aiProvider]
  const initials = agent.name
    .split(' ')
    .map((n) => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2)

  return (
    <>
      <Card className="group hover:shadow-lg transition-all duration-200 hover:border-primary-500/20">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10">
                <AvatarFallback className="bg-gradient-primary text-white">
                  {initials}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <CardTitle className="text-lg truncate">{agent.name}</CardTitle>
                <CardDescription className="text-sm">
                  {agent.description.length > 60
                    ? `${agent.description.slice(0, 60)}...`
                    : agent.description}
                </CardDescription>
              </div>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100 transition-opacity">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onEdit(agent)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Agent
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setShowDeleteDialog(true)}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Agent
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Status and Provider */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Switch
                checked={agent.isActive}
                onCheckedChange={handleStatusToggle}
                disabled={toggleStatus.isPending}
              />
              <span className="text-sm text-muted-foreground">
                {agent.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
            <Badge variant="outline" className="text-xs">
              {providerInfo.name}
            </Badge>
          </div>

          {/* AI Model */}
          <div className="text-sm">
            <span className="text-muted-foreground">Model: </span>
            <span className="font-medium">{agent.aiModel}</span>
          </div>

          {/* Topics Preview */}
          <div>
            <span className="text-sm text-muted-foreground">Topics: </span>
            <div className="flex flex-wrap gap-1 mt-1">
              {agent.personaData.topics.slice(0, 3).map((topic, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {topic}
                </Badge>
              ))}
              {agent.personaData.topics.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{agent.personaData.topics.length - 3} more
                </Badge>
              )}
            </div>
          </div>

          {/* Metrics */}
          <div className="grid grid-cols-2 gap-4 pt-2 border-t">
            <div className="flex items-center space-x-2">
              <Zap className="h-4 w-4 text-primary-500" />
              <div>
                <p className="text-sm font-medium">{formatNumber(agent.tweetsGenerated)}</p>
                <p className="text-xs text-muted-foreground">Tweets</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-green-500" />
              <div>
                <p className="text-sm font-medium">{agent.engagementRate.toFixed(1)}%</p>
                <p className="text-xs text-muted-foreground">Engagement</p>
              </div>
            </div>
          </div>

          {/* Daily Limit */}
          <div className="text-xs text-muted-foreground">
            Daily limit: {agent.maxDailyTweets} tweets
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Agent</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{agent.name}"? This action cannot be undone and will
              also delete all associated tweets and memories.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700"
              disabled={deleteAgent.isPending}
            >
              {deleteAgent.isPending ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}