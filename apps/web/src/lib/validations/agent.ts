import { z } from 'zod'

export const personaDataSchema = z.object({
  personality: z.string().min(1, 'Personality is required').max(500, 'Personality too long'),
  tone: z.string().min(1, 'Tone is required').max(100, 'Tone too long'),
  topics: z.array(z.string().min(1)).min(1, 'At least one topic is required').max(20, 'Too many topics'),
  writingStyle: z.string().min(1, 'Writing style is required').max(500, 'Writing style too long'),
  restrictions: z.array(z.string().min(1)).max(20, 'Too many restrictions'),
  expertise: z.array(z.string().min(1)).max(20, 'Too many expertise areas').optional(),
  communicationStyle: z.string().max(500, 'Communication style too long').optional(),
  targetAudience: z.string().max(500, 'Target audience too long').optional(),
  contentThemes: z.array(z.string().min(1)).max(20, 'Too many content themes').optional(),
  avoidTopics: z.array(z.string().min(1)).max(20, 'Too many avoid topics').optional(),
})

export const createAgentSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().min(10, 'Description must be at least 10 characters').max(500, 'Description too long'),
  personaData: personaDataSchema,
  aiProvider: z.enum(['openai', 'google'], {
    required_error: 'AI provider is required',
  }),
  aiModel: z.string().min(1, 'AI model is required'),
  maxDailyTweets: z.number().min(1, 'Must be at least 1').max(100, 'Cannot exceed 100'),
})

export const updateAgentSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  description: z.string().min(10, 'Description must be at least 10 characters').max(500, 'Description too long').optional(),
  personaData: personaDataSchema.optional(),
  aiProvider: z.enum(['openai', 'google']).optional(),
  aiModel: z.string().min(1, 'AI model is required').optional(),
  isActive: z.boolean().optional(),
  maxDailyTweets: z.number().min(1, 'Must be at least 1').max(100, 'Cannot exceed 100').optional(),
})

export const agentQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  search: z.string().optional(),
  status: z.enum(['active', 'inactive', 'all']).default('all'),
  sortBy: z.enum(['createdAt', 'name', 'tweetsGenerated', 'engagementRate']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
})

export type CreateAgentData = z.infer<typeof createAgentSchema>
export type UpdateAgentData = z.infer<typeof updateAgentSchema>
export type AgentQueryParams = z.infer<typeof agentQuerySchema>
export type PersonaData = z.infer<typeof personaDataSchema>