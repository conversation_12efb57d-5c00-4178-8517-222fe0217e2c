import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'
import { prisma } from '@/lib/database'
import { updateAgentSchema } from '@/lib/validations/agent'
import { Prisma } from '@prisma/client'

interface RouteParams {
  params: {
    id: string
  }
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await requireAuth()
    const { id } = params

    const agent = await prisma.agent.findFirst({
      where: {
        id,
        userId: session.userId,
      },
    })

    if (!agent) {
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(agent)
  } catch (error) {
    console.error('Get agent error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch agent' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await requireAuth()
    const { id } = params
    const body = await request.json()

    // Validate request body
    const validationResult = updateAgentSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid agent data', details: validationResult.error.errors },
        { status: 400 }
      )
    }

    const updateData = validationResult.data

    // Check if agent exists and belongs to user
    const existingAgent = await prisma.agent.findFirst({
      where: {
        id,
        userId: session.userId,
      },
    })

    if (!existingAgent) {
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      )
    }

    // Update agent
    const agent = await prisma.agent.update({
      where: { id },
      data: updateData,
    })

    return NextResponse.json(agent)
  } catch (error) {
    console.error('Update agent error:', error)
    
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2002') {
        return NextResponse.json(
          { error: 'Agent name must be unique' },
          { status: 409 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to update agent' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await requireAuth()
    const { id } = params

    // Check if agent exists and belongs to user
    const existingAgent = await prisma.agent.findFirst({
      where: {
        id,
        userId: session.userId,
      },
    })

    if (!existingAgent) {
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      )
    }

    // Delete agent (cascade will handle related records)
    await prisma.agent.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Agent deleted successfully' })
  } catch (error) {
    console.error('Delete agent error:', error)
    return NextResponse.json(
      { error: 'Failed to delete agent' },
      { status: 500 }
    )
  }
}