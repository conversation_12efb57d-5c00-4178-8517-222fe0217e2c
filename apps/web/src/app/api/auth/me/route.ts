import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'

export async function GET(request: NextRequest) {
  try {
    const session = await requireAuth()

    return NextResponse.json({
      user: {
        id: session.userId,
        email: session.email,
        name: session.name,
        avatar: session.avatar,
      },
    })
  } catch (error) {
    console.error('Session validation error:', error)
    return NextResponse.json(
      { error: 'Session validation failed' },
      { status: 401 }
    )
  }
}