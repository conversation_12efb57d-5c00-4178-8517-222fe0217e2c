import { NextRequest, NextResponse } from "next/server"
import { verifyJWT } from "@/lib/auth/jwt"
import { prisma } from "@/lib/database"

interface RouteParams {
  params: {
    accountId: string
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Get the JWT token from cookies
    const token = request.cookies.get("auth-token")?.value

    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Verify the JWT token
    const payload = await verifyJWT(token)
    if (!payload || !payload.userId) {
      return NextResponse.json(
        { error: "Invalid token" },
        { status: 401 }
      )
    }

    const { accountId } = params

    // Find the account and verify ownership
    const account = await prisma.account.findFirst({
      where: {
        id: accountId,
        userId: payload.userId,
      }
    })

    if (!account) {
      return NextResponse.json(
        { error: "Account not found or access denied" },
        { status: 404 }
      )
    }

    // Check if this is the user's primary account
    const userAccountsCount = await prisma.account.count({
      where: { userId: payload.userId }
    })

    if (userAccountsCount <= 1) {
      return NextResponse.json(
        { error: "Cannot disconnect the last connected account" },
        { status: 400 }
      )
    }

    // Delete the account
    await prisma.account.delete({
      where: { id: accountId }
    })

    return NextResponse.json({
      success: true,
      message: "Account disconnected successfully"
    })

  } catch (error) {
    console.error("Error disconnecting account:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Get the JWT token from cookies
    const token = request.cookies.get("auth-token")?.value

    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Verify the JWT token
    const payload = await verifyJWT(token)
    if (!payload || !payload.userId) {
      return NextResponse.json(
        { error: "Invalid token" },
        { status: 401 }
      )
    }

    const { accountId } = params

    // Find the account and verify ownership
    const account = await prisma.account.findFirst({
      where: {
        id: accountId,
        userId: payload.userId,
      },
      select: {
        id: true,
        provider: true,
        providerAccountId: true,
        type: true,
        createdAt: true,
        updatedAt: true,
        expires_at: true,
        // Don't include sensitive token data in GET requests
      }
    })

    if (!account) {
      return NextResponse.json(
        { error: "Account not found or access denied" },
        { status: 404 }
      )
    }

    // Transform account data for frontend
    const accountData = {
      id: account.id,
      provider: account.provider,
      providerAccountId: account.providerAccountId,
      type: account.type,
      isActive: account.expires_at ? new Date(account.expires_at * 1000) > new Date() : true,
      connectedAt: account.createdAt.toISOString(),
      lastUsed: account.updatedAt.toISOString(),
    }

    return NextResponse.json({
      success: true,
      account: accountData
    })

  } catch (error) {
    console.error("Error fetching account:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
