@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* XTask Custom Colors */
    --primary-50: 250 245 255;
    --primary-100: 243 232 255;
    --primary-200: 233 213 255;
    --primary-300: 216 180 254;
    --primary-400: 196 181 253;
    --primary-500: 139 92 246; /* #8b5cf6 */
    --primary-600: 124 58 237;
    --primary-700: 109 40 217;
    --primary-800: 91 33 182;
    --primary-900: 76 29 149;
    --primary-950: 46 16 101;
    --primary-foreground: 255 255 255;
    
    /* Dark Theme Colors */
    --dark-bg: 10 10 10; /* #0a0a0a */
    --dark-surface: 26 26 26; /* #1a1a1a */
    --dark-border: 42 42 42; /* #2a2a2a */
    
    /* shadcn/ui Variables */
    --background: 10 10 10; /* Dark background */
    --foreground: 0 0% 98%;
    --card: 26 26 26; /* Dark surface */
    --card-foreground: 0 0% 98%;
    --popover: 26 26 26;
    --popover-foreground: 0 0% 98%;
    --primary: 139 92 246; /* Primary purple */
    --primary-foreground: 255 255 255;
    --secondary: 42 42 42; /* Dark border */
    --secondary-foreground: 0 0% 98%;
    --muted: 42 42 42;
    --muted-foreground: 240 5% 64.9%;
    --accent: 139 92 246; /* Primary purple accent */
    --accent-foreground: 255 255 255; /* White text on purple accent */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 42 42 42; /* Dark border */
    --input: 42 42 42;
    --ring: 139 92 246; /* Primary purple */
    --radius: 0.75rem;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }

  .light {
    /* Light theme overrides */
    --background: 255 255 255;
    --foreground: 0 0% 3.9%;
    --card: 255 255 255;
    --card-foreground: 0 0% 3.9%;
    --popover: 255 255 255;
    --popover-foreground: 0 0% 3.9%;
    --primary: 139 92 246; /* Keep primary purple */
    --primary-foreground: 255 255 255;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 139 92 246; /* Keep primary purple */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-card;
}

::-webkit-scrollbar-thumb {
  @apply bg-border rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary rounded-full;
}

/* Smooth transitions */
* {
  @apply transition-colors duration-200;
}